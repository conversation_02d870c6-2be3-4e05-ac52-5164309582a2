// ignore_for_file: constant_identifier_names

part of 'app_pages.dart';
// DO NOT EDIT. This is code generated via package:get_cli/get_cli.dart

abstract class Routes {
  Routes._();
  static const HOME = _Paths.HOME;
  static const BOTTOMBAR = _Paths.BOTTOMBAR;
  static const NEWS = _Paths.NEWS;
  static const EVENTS = _Paths.EVENTS;
  static const ACCOUNT = _Paths.ACCOUNT;
  static const GUEST = _Paths.GUEST;
  static const LOGIN = _Paths.LOGIN;
  static const REGISTER = _Paths.REGISTER;
  static const HOME_DETAIL = _Paths.HOME_DETAIL;
  static const EVENTS_DETAIL = _Paths.EVENTS_DETAIL;
  static const BOOKING = _Paths.BOOKING;
  static const CHECK_IN = _Paths.CHECK_IN;
  static const BOOKING_SUCCESS = _Paths.BOOKING_SUCCESS;
  static const FORGOT_PASSWORD_EMAIL = _Paths.FORGOT_PASSWORD_EMAIL;
  static const FORGOT_PASSWORD_CHECK_CODE = _Paths.FORGOT_PASSWORD_CHECK_CODE;
  static const FORGOT_PASSWORD_RESET = _Paths.FORGOT_PASSWORD_RESET;
  static const PROFILE_EDIT = _Paths.PROFILE_EDIT;
  static const EVENTS_FORM = _Paths.EVENTS_FORM;
  static const EVENTS_FORM_SUCCESS = _Paths.EVENTS_FORM_SUCCESS;
  static const EVENTS_FORM_SIGNED = _Paths.EVENTS_FORM_SIGNED;
  static const EVENTS_FORM_UPDATE = _Paths.EVENTS_FORM_UPDATE;
  static const EVENTS_FORM_UPDATE_SUCCESS = _Paths.EVENTS_FORM_UPDATE_SUCCESS;
  static const VEHICLES = _Paths.VEHICLES;
  static const VEHICLES_ADD = _Paths.VEHICLES_ADD;
  static const VEHICLES_EDIT = _Paths.VEHICLES_EDIT;
  static const KCONFIRM_DIALOG = _Paths.KCONFIRM_DIALOG;
  static const NOTIFICATION = _Paths.NOTIFICATION;
  static const NOTIFICATION_DETAIL = _Paths.NOTIFICATION_DETAIL;
  static const EVENTS_FORM_SIGN = _Paths.EVENTS_FORM_SIGN;
  static const EVENTS_FORM_SIGN_SUCCESS = _Paths.EVENTS_FORM_SIGN_SUCCESS;
  static const ACCOUNT_DELETE = _Paths.ACCOUNT_DELETE;
  static const VERIFY_EMAIL = _Paths.VERIFY_EMAIL;
  static const MAKE_PAYMENT = _Paths.MAKE_PAYMENT;
  static const SOCIAL_LOGIN_FORM = _Paths.SOCIAL_LOGIN_FORM;
  static const CHAT_LIST = _Paths.CHAT_LIST;
  static const CHAT = _Paths.CHAT;
  static const LEADERBOARD = _Paths.LEADERBOARD;
  static const PERSONAL_RESULTS = _Paths.PERSONAL_RESULTS;
  static const LEADERBOARD_DETAIL = _Paths.LEADERBOARD_DETAIL;
  static const FORCE_UPGRADE = _Paths.FORCE_UPGRADE;
  static const PERLAPS = _Paths.PERLAPS;
  static const GALLERY = _Paths.GALLERY;
  static const GALLERY_DETAIL = _Paths.GALLERY_DETAIL;
  static const JOIN_WAITING_LIST = _Paths.JOIN_WAITING_LIST;
  static const ADDON = _Paths.ADDON;
  static const BOOKING_DETAILS = _Paths.BOOKING_DETAILS;
  static const UPDATE_MY_BOOKING = _Paths.UPDATE_MY_BOOKING;
  static const SELL_MY_SLOT = _Paths.SELL_MY_SLOT;
  static const REWARD = _Paths.REWARD;
  static const REWARD_DETAIL = _Paths.REWARD_DETAIL;
  static const SELECT_COUPON_CODE = _Paths.SELECT_COUPON_CODE;
  static const STORE = _Paths.STORE;
  static const PRODUCT_DETAIL = _Paths.PRODUCT_DETAIL;
  static const CAR_LISTING_DETAIL = _Paths.CAR_LISTING_DETAIL;
  static const PASSENGER_FORM = _Paths.PASSENGER_FORM;
  static const GROUP = _Paths.GROUP;
  static const CLAIM_REBATE = _Paths.CLAIM_REBATE;
  static const REWARD_LIFESTYLE_DETAIL = _Paths.REWARD_LIFESTYLE_DETAIL;
  static const CART = _Paths.CART;
  static const CHECKOUT = _Paths.CHECKOUT;
  static const SHIPPING_ADDRESS = _Paths.SHIPPING_ADDRESS;
  static const SHIPPING_ADDRESS_ADD = _Paths.SHIPPING_ADDRESS_ADD;
  static const SHIPPING_ADDRESS_EDIT = _Paths.SHIPPING_ADDRESS_EDIT;
  static const PRODUCT_PAYMENT = _Paths.PRODUCT_PAYMENT;
  static const ORDER_STATUS = _Paths.ORDER_STATUS;
  static const ORDER_STATUS_DETAIL = _Paths.ORDER_STATUS_DETAIL;
}

abstract class _Paths {
  _Paths._();
  static const HOME = '/home';
  static const BOTTOMBAR = '/';
  static const NEWS = '/news';
  static const EVENTS = '/events';
  static const ACCOUNT = '/account';
  static const GUEST = '/guest';
  static const LOGIN = '/login';
  static const REGISTER = '/register';
  static const HOME_DETAIL = '/home-detail';
  static const EVENTS_DETAIL = '/events-detail';
  static const BOOKING = '/booking';
  static const CHECK_IN = '/check-in';
  static const BOOKING_SUCCESS = '/booking-success';
  static const FORGOT_PASSWORD_EMAIL = '/forgot-password-email';
  static const FORGOT_PASSWORD_CHECK_CODE = '/forgot-password-check-code';
  static const FORGOT_PASSWORD_RESET = '/forgot-password-reset';
  static const PROFILE_EDIT = '/profile-edit';
  static const EVENTS_FORM = '/events-form';
  static const EVENTS_FORM_SUCCESS = '/events-form-success';
  static const EVENTS_FORM_SIGNED = '/events-form-signed';
  static const EVENTS_FORM_UPDATE = '/events-form-update';
  static const EVENTS_FORM_UPDATE_SUCCESS = '/events-form-update-success';
  static const VEHICLES = '/vehicles';
  static const VEHICLES_ADD = '/vehicles-add';
  static const VEHICLES_EDIT = '/vehicles-edit';
  static const KCONFIRM_DIALOG = '/kconfirm-dialog';
  static const NOTIFICATION = '/notification';
  static const NOTIFICATION_DETAIL = '/notification-detail';
  static const EVENTS_FORM_SIGN = '/events-form-sign';
  static const EVENTS_FORM_SIGN_SUCCESS = '/events-form-sign-success';
  static const ACCOUNT_DELETE = '/account-delete';
  static const VERIFY_EMAIL = '/verify-email';
  static const MAKE_PAYMENT = '/make-payment';
  static const SOCIAL_LOGIN_FORM = '/social-login-form';
  static const CHAT_LIST = '/chat-list';
  static const CHAT = '/chat';
  static const LEADERBOARD = '/leaderboard';
  static const PERSONAL_RESULTS = '/personal-results';
  static const LEADERBOARD_DETAIL = '/leaderboard-detail';
  static const FORCE_UPGRADE = '/force-upgrade';
  static const PERLAPS = '/perlaps';
  static const GALLERY = '/gallery';
  static const GALLERY_DETAIL = '/gallery-detail';
  static const JOIN_WAITING_LIST = '/join-waiting-list';
  static const ADDON = '/addon';
  static const BOOKING_DETAILS = '/booking-details';
  static const UPDATE_MY_BOOKING = '/update-my-booking';
  static const SELL_MY_SLOT = '/sell-my-slot';
  static const REWARD = '/reward';
  static const REWARD_DETAIL = '/reward-detail';
  static const SELECT_COUPON_CODE = '/select-coupon-code';
  static const STORE = '/store';
  static const PRODUCT_DETAIL = '/product-detail';
  static const CAR_LISTING_DETAIL = '/car-listing-detail';
  static const PASSENGER_FORM = '/passenger-form';
  static const GROUP = '/group';
  static const CLAIM_REBATE = '/claim-rebate';
  static const REWARD_LIFESTYLE_DETAIL = '/reward-lifestyle-detail';
  static const CART = '/cart';
  static const CHECKOUT = '/checkout';
  static const SHIPPING_ADDRESS = '/shipping-address';
  static const SHIPPING_ADDRESS_ADD = '/shipping-address-add';
  static const SHIPPING_ADDRESS_EDIT = '/shipping-address-edit';
  static const PRODUCT_PAYMENT = '/product-payment';
  static const ORDER_STATUS = '/order-status';
  static const ORDER_STATUS_DETAIL = '/order-status-detail';
}
