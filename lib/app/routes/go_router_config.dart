import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../modules/login/views/login_view_hybrid.dart';
import '../modules/profile_edit/views/profile_edit_view.dart';
import '../modules/profile_edit/bindings/profile_edit_binding.dart';
import '../widgets/navigation_test_widget.dart';

/// go_router configuration for migrated routes
class GoRouterConfig {
  static final GlobalKey<NavigatorState> _rootNavigatorKey =
      GlobalKey<NavigatorState>();
  static final GlobalKey<NavigatorState> _shellNavigatorKey =
      GlobalKey<NavigatorState>();

  static GlobalKey<NavigatorState> get rootNavigatorKey => _rootNavigatorKey;
  static GlobalKey<NavigatorState> get shellNavigatorKey => _shellNavigatorKey;

  static GoRouter createRouter() {
    return GoRouter(
      navigatorKey: _rootNavigatorKey,
      initialLocation: '/',
      debugLogDiagnostics: true,
      routes: [
        // Login route - simple route to start migration
        GoRoute(
          path: '/login',
          name: 'login',
          builder: (context, state) {
            return const LoginViewHybrid();
          },
        ),

        // Profile Edit route - another simple route
        GoRoute(
          path: '/profile-edit',
          name: 'profile-edit',
          builder: (context, state) {
            // Initialize GetX binding for this route
            ProfileEditBinding().dependencies();
            return const ProfileEditView();
          },
        ),

        // Navigation test route
        GoRoute(
          path: '/navigation-test',
          name: 'navigation-test',
          builder: (context, state) {
            return const NavigationTestWidget();
          },
        ),

        // Fallback route - redirects to GetX router
        GoRoute(
          path: '/',
          name: 'fallback',
          builder: (context, state) {
            // This should never be reached as we redirect to GetX
            return const Scaffold(
              body: Center(
                child: CircularProgressIndicator(),
              ),
            );
          },
        ),
      ],

      // Error handling
      errorBuilder: (context, state) {
        return Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                Text('Route not found: ${state.uri}'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => context.go('/'),
                  child: const Text('Go Home'),
                ),
              ],
            ),
          ),
        );
      },

      // Redirect logic
      redirect: (context, state) {
        final location = state.uri.path;

        // List of routes handled by go_router
        const goRouterRoutes = {
          '/login',
          '/profile-edit',
        };

        // If this route is not handled by go_router, let GetX handle it
        if (!goRouterRoutes.contains(location)) {
          return null; // Let the route proceed normally
        }

        return null; // No redirect needed
      },
    );
  }
}

/// Extension to get arguments from go_router state
extension GoRouterStateExtension on GoRouterState {
  /// Get arguments passed from GetX-style navigation
  dynamic get arguments {
    final extra = this.extra;
    if (extra is Map<String, dynamic> && extra.containsKey('arguments')) {
      return extra['arguments'];
    }
    return null;
  }
}
