import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';

import '../routes/go_router_config.dart';
import '../routes/app_pages.dart';
import '../services/navigation_service.dart';
import '../routes/route_names.dart';

/// Hybrid app that supports both GetX and go_router
/// This allows gradual migration from GetX to go_router
class HybridApp extends StatefulWidget {
  final String initialRoute;
  final FirebaseAnalytics analytics;

  const HybridApp({
    super.key,
    required this.initialRoute,
    required this.analytics,
  });

  @override
  State<HybridApp> createState() => _HybridAppState();
}

class _HybridAppState extends State<HybridApp> {
  late final GoRouter _goRouter;
  late final NavigationService _navigationService;

  @override
  void initState() {
    super.initState();

    // Initialize go_router
    _goRouter = GoRouterConfig.createRouter();

    // Initialize navigation service
    _navigationService = Get.put(NavigationService());
    _navigationService.setGoRouter(_goRouter);
  }

  @override
  Widget build(BuildContext context) {
    // Check if initial route should use go_router
    if (RouteNames.isMigratedToGoRouter(widget.initialRoute)) {
      return _buildGoRouterApp();
    } else {
      return _buildGetXApp();
    }
  }

  /// Build app with go_router for migrated routes
  Widget _buildGoRouterApp() {
    return MaterialApp.router(
      debugShowCheckedModeBanner: false,
      title: "Automoment",
      routerConfig: _goRouter,
      builder: (context, child) {
        return EasyLoading.init()(context, child);
      },
    );
  }

  /// Build app with GetX for non-migrated routes
  Widget _buildGetXApp() {
    return GetMaterialApp(
      debugShowCheckedModeBanner: false,
      title: "Automoment",
      initialRoute: widget.initialRoute,
      getPages: AppPages.routes,
      builder: (context, child) {
        return EasyLoading.init()(context, child);
      },
      navigatorKey: GoRouterConfig.rootNavigatorKey,
      navigatorObservers: [
        FirebaseAnalyticsObserver(analytics: widget.analytics),
        SentryNavigatorObserver(),
      ],
      onGenerateRoute: (settings) {
        // Check if this route should be handled by go_router
        if (RouteNames.isMigratedToGoRouter(settings.name ?? '')) {
          // Navigate to go_router
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _goRouter.go(settings.name!);
          });

          // Return a temporary loading page
          return MaterialPageRoute(
            builder: (context) => const Scaffold(
              body: Center(
                child: CircularProgressIndicator(),
              ),
            ),
          );
        }
        return null;
      },
    );
  }
}

/// Custom route information parser for hybrid routing
class HybridRouteInformationParser
    extends RouteInformationParser<RouteInformation> {
  @override
  Future<RouteInformation> parseRouteInformation(
      RouteInformation routeInformation) async {
    return routeInformation;
  }

  @override
  RouteInformation restoreRouteInformation(RouteInformation configuration) {
    return configuration;
  }
}

/// Custom router delegate for hybrid routing
class HybridRouterDelegate extends RouterDelegate<RouteInformation>
    with ChangeNotifier, PopNavigatorRouterDelegateMixin<RouteInformation> {
  @override
  GlobalKey<NavigatorState> get navigatorKey => GoRouterConfig.rootNavigatorKey;

  @override
  RouteInformation? get currentConfiguration {
    // Return current route information
    return RouteInformation(
      uri: Uri.parse(Get.currentRoute),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Navigator(
      key: navigatorKey,
      pages: [
        MaterialPage(
          child: Container(), // This will be handled by GetX or go_router
        ),
      ],
      onDidRemovePage: (page) {
        // Handle page removal
      },
    );
  }

  @override
  Future<void> setNewRoutePath(RouteInformation configuration) async {
    // Handle route changes
    final location = configuration.uri.path;

    if (RouteNames.isMigratedToGoRouter(location)) {
      // Let go_router handle this
      return;
    } else {
      // Let GetX handle this
      Get.toNamed(location);
    }
  }
}
