import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../routes/route_names.dart';
import '../services/navigation_service.dart';

/// Test widget to verify hybrid navigation is working
class NavigationTestWidget extends StatelessWidget {
  const NavigationTestWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final navigationService = NavigationService.instance;
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Navigation Test'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Hybrid Navigation Test',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            
            const Text(
              'Routes migrated to go_router:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 10),
            
            ElevatedButton(
              onPressed: () {
                navigationService.toNamed(RouteNames.login);
              },
              child: const Text('Test Login (go_router)'),
            ),
            
            ElevatedButton(
              onPressed: () {
                navigationService.toNamed(RouteNames.profileEdit);
              },
              child: const Text('Test Profile Edit (go_router)'),
            ),
            
            const SizedBox(height: 20),
            const Text(
              'Routes still using GetX:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 10),
            
            ElevatedButton(
              onPressed: () {
                navigationService.toNamed(RouteNames.account);
              },
              child: const Text('Test Account (GetX)'),
            ),
            
            ElevatedButton(
              onPressed: () {
                navigationService.toNamed(RouteNames.events);
              },
              child: const Text('Test Events (GetX)'),
            ),
            
            ElevatedButton(
              onPressed: () {
                navigationService.toNamed(RouteNames.news);
              },
              child: const Text('Test News (GetX)'),
            ),
            
            const SizedBox(height: 30),
            
            const Text(
              'Current Route Information:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 10),
            
            Obx(() => Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Current Route: ${navigationService.currentRoute}'),
                    Text('Can Pop: ${navigationService.canPop}'),
                    const SizedBox(height: 10),
                    Text('Migrated Routes: ${RouteNames.migratedRoutes.join(', ')}'),
                  ],
                ),
              ),
            )),
            
            const SizedBox(height: 20),
            
            ElevatedButton(
              onPressed: () {
                navigationService.back();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Go Back'),
            ),
          ],
        ),
      ),
    );
  }
}
