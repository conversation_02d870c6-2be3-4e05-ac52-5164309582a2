import 'dart:io';

import 'package:automoment/app/constants/app_text_styles.dart';
import 'package:automoment/app/controllers/nav_history_controller.dart';
import 'package:flutter/material.dart';
import 'package:email_validator/email_validator.dart';
import 'package:get/get.dart';

import '../../../constants/app_color.dart';
import '../../../constants/app_config.dart';
import '../../../routes/route_names.dart';
import '../../../services/navigation_service.dart';
import '../../shared/kmessage_dialog/views/kmessage_dialog_view.dart';
import '../controllers/login_controller.dart';

/// Hybrid login view that works with both GetX and go_router
class LoginViewHybrid extends StatefulWidget {
  const LoginViewHybrid({super.key});

  @override
  State<LoginViewHybrid> createState() => _LoginViewHybridState();
}

class _LoginViewHybridState extends State<LoginViewHybrid> {
  final LoginController controller = Get.put(LoginController());
  final NavHistoryController navHistoryController =
      Get.put(NavHistoryController());
  late final NavigationService navigationService;

  @override
  void initState() {
    super.initState();
    navigationService = NavigationService.instance;
  }

  @override
  void dispose() {
    Get.delete<LoginController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: SafeArea(
        top: false,
        child: SingleChildScrollView(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          reverse: false,
          child: Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20), topRight: Radius.circular(20)),
              boxShadow: [
                BoxShadow(
                    offset: Offset(0, -5),
                    blurRadius: 15,
                    color: Colors.black12),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                getHeader(),
                buildLoginForm(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Padding getHeader() {
    return Padding(
        padding: const EdgeInsets.all(10),
        child: Column(
          children: [
            Row(
              children: [
                const Spacer(),
                IconButton(
                  onPressed: () {
                    // Use hybrid navigation for back
                    navigationService.back();
                  },
                  icon: const Icon(Icons.close),
                  iconSize: 30,
                  color: Colors.black,
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.symmetric(
                  horizontal: AppConfig.defaultPadding),
              child: Container(
                  alignment: Alignment.topLeft,
                  child: const Text(
                    "Welcome Back!",
                    style: AppTextStyles.bigHeaderText,
                  )),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(
                  horizontal: AppConfig.defaultPadding),
              child: Container(
                  alignment: Alignment.topLeft,
                  child: const Text(
                    "Login now and check out the latest deals available!",
                    style: AppTextStyles.normalText,
                  )),
            ),
          ],
        ));
  }

  Padding buildLoginForm(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(
          left: AppConfig.defaultPadding * 2,
          right: AppConfig.defaultPadding * 2,
          bottom: AppConfig.defaultPadding * 2),
      child: Column(
        children: [
          Form(
            key: controller.formKey.value,
            child: Column(
              children: [
                TextFormField(
                  controller: controller.emailController,
                  keyboardType: TextInputType.emailAddress,
                  autocorrect: false,
                  decoration: const InputDecoration(
                    labelText: "Email",
                    hintText: "",
                  ),
                  validator: (value) {
                    if (value!.isEmpty) {
                      return 'Required';
                    }

                    if (!EmailValidator.validate(value)) {
                      return "Invalid email pattern";
                    }
                    return null;
                  },
                ),
                Obx(() => TextFormField(
                      controller: controller.passwordController,
                      keyboardType: TextInputType.text,
                      autocorrect: false,
                      obscureText: controller.isPasswordHidden.value,
                      decoration: InputDecoration(
                        labelText: "Password",
                        hintText: "",
                        suffixIcon: IconButton(
                          icon: Icon(
                            controller.isPasswordHidden.value
                                ? Icons.visibility_off
                                : Icons.visibility,
                          ),
                          onPressed: () {
                            controller.isPasswordHidden.value =
                                !controller.isPasswordHidden.value;
                          },
                        ),
                      ),
                      validator: (value) {
                        if (value!.isEmpty) {
                          return 'Required';
                        }
                        return null;
                      },
                    )),
                Padding(
                  padding: const EdgeInsets.symmetric(
                      vertical: AppConfig.defaultPadding * 2),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      GestureDetector(
                        child: const Text("Forgot password?"),
                        onTap: () {
                          // Use hybrid navigation
                          navigationService
                              .toNamed(RouteNames.forgotPasswordEmail);
                        },
                      ),
                    ],
                  ),
                ),
                //remember me checkbox
                Padding(
                  padding: const EdgeInsets.only(top: 10),
                  child: Row(children: [
                    SizedBox(
                      width: 24,
                      height: 24,
                      child: Obx(() => Checkbox(
                            activeColor: AppColor.secondaryColor,
                            value: controller.isChecked.value,
                            onChanged: (value) {
                              controller.isChecked.value =
                                  !controller.isChecked.value;
                            },
                          )),
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    const Flexible(
                      child: Column(children: [
                        Text("Remember me"),
                      ]),
                    ),
                  ]),
                ),
                const SizedBox(
                  height: 30,
                ),
                ElevatedButton(
                    style: ButtonStyle(
                        foregroundColor:
                            WidgetStateProperty.all<Color>(Colors.white),
                        backgroundColor: WidgetStateProperty.all<Color>(
                            AppColor.primaryButtonColor),
                        shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                            RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(18.0),
                                side: const BorderSide(
                                    color: AppColor.primaryButtonColor)))),
                    onPressed: () async {
                      if (controller.formKey.value.currentState!.validate()) {
                        if (await controller.login()) {
                          _handleSuccessfulLogin();
                          debugPrint(
                              "login success, token: ${controller.token}");
                        } else {
                          Get.dialog(
                              KMessageDialogView(content: controller.message),
                              barrierDismissible: false);
                          debugPrint("login failed ${controller.message}");
                        }
                      } else {
                        debugPrint("not ok");
                      }
                    },
                    child: Container(
                      padding: const EdgeInsets.only(left: 20, right: 20),
                      height: 50,
                      child: const Center(
                        child: Text("Sign In", style: TextStyle(fontSize: 16)),
                      ),
                    )),
                const SizedBox(
                  height: 10,
                ),
                // Google Sign In Button
                _buildGoogleSignInButton(),
                const SizedBox(
                  height: 10,
                ),
                // Apple Sign In Button (iOS only)
                if (Platform.isIOS) _buildAppleSignInButton(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGoogleSignInButton() {
    return ElevatedButton(
        style: ButtonStyle(
            foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
            backgroundColor:
                WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
            shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(18.0),
                    side:
                        const BorderSide(color: AppColor.primaryButtonColor)))),
        onPressed: () async {
          if (await controller.signInWithGoogle()) {
            _handleSocialLogin("Google");
          } else {
            Get.dialog(KMessageDialogView(content: controller.message),
                barrierDismissible: false);
            debugPrint("login failed ${controller.message}");
          }
        },
        child: Container(
          padding: const EdgeInsets.only(left: 20, right: 20),
          height: 50,
          child: Row(
            children: [
              const Spacer(),
              Image.asset("assets/images/google.png", height: 20),
              const SizedBox(width: 10),
              const Text("Sign In with Google", style: TextStyle(fontSize: 16)),
              const Spacer(),
            ],
          ),
        ));
  }

  Widget _buildAppleSignInButton() {
    return ElevatedButton(
        style: ButtonStyle(
            foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
            backgroundColor:
                WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
            shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(18.0),
                    side:
                        const BorderSide(color: AppColor.primaryButtonColor)))),
        onPressed: () async {
          if (await controller.signInWithApple()) {
            _handleSocialLogin("Apple");
          } else {
            Get.dialog(KMessageDialogView(content: controller.message),
                barrierDismissible: false);
            debugPrint("login failed ${controller.message}");
          }
        },
        child: Container(
          padding: const EdgeInsets.only(left: 20, right: 20),
          height: 50,
          child: Row(
            children: [
              const Spacer(),
              Image.asset("assets/images/apple.png", height: 20),
              const SizedBox(width: 10),
              const Text("Sign In with Apple", style: TextStyle(fontSize: 16)),
              const Spacer(),
            ],
          ),
        ));
  }

  void _handleSuccessfulLogin() {
    if (navHistoryController.backPageArgs == null) {
      navigationService.offAllNamed(RouteNames.bottombar);
    } else {
      navigationService.offAllNamed(RouteNames.bottombar);
      navigationService.toNamed(
        navHistoryController.backPage,
        arguments: navHistoryController.backPageArgs,
      );
    }
  }

  void _handleSocialLogin(String provider) {
    if (controller.isAllRequiredUserFieldsFilled()) {
      _handleSuccessfulLogin();
    } else {
      // Note: SOCIAL_LOGIN_FORM route needs to be migrated or handled appropriately
      Get.offAllNamed('/social-login-form', arguments: provider);
    }
  }
}
