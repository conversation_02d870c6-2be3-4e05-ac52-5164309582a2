import 'package:get/get.dart';
import 'package:go_router/go_router.dart';

import '../routes/route_names.dart';

/// Hybrid navigation service that supports both GetX and go_router
/// This allows gradual migration from GetX router to go_router
class NavigationService extends GetxService {
  static NavigationService get instance => Get.find<NavigationService>();

  GoRouter? _goRouter;

  /// Set the go_router instance
  void setGoRouter(GoRouter router) {
    _goRouter = router;
  }

  /// Check if a route is handled by go_router
  bool _isGoRouterRoute(String routeName) {
    if (_goRouter == null) return false;
    return RouteNames.isMigratedToGoRouter(routeName);
  }

  /// Navigate to a named route
  /// Automatically chooses between GetX and go_router based on route
  Future<T?>? toNamed<T>(
    String routeName, {
    dynamic arguments,
    Map<String, String>? parameters,
  }) {
    if (_isGoRouterRoute(routeName)) {
      return _navigateWithGoRouter<T>(routeName,
          arguments: arguments, parameters: parameters);
    } else {
      return Get.toNamed<T>(routeName,
          arguments: arguments, parameters: parameters);
    }
  }

  /// Navigate and remove all previous routes
  Future<T?>? offAllNamed<T>(
    String routeName, {
    dynamic arguments,
    Map<String, String>? parameters,
  }) {
    if (_isGoRouterRoute(routeName)) {
      return _goWithGoRouter<T>(routeName,
          arguments: arguments, parameters: parameters);
    } else {
      return Get.offAllNamed<T>(routeName,
          arguments: arguments, parameters: parameters);
    }
  }

  /// Navigate and remove the previous route
  Future<T?>? offNamed<T>(
    String routeName, {
    dynamic arguments,
    Map<String, String>? parameters,
  }) {
    if (_isGoRouterRoute(routeName)) {
      return _goWithGoRouter<T>(routeName,
          arguments: arguments, parameters: parameters);
    } else {
      return Get.offNamed<T>(routeName,
          arguments: arguments, parameters: parameters);
    }
  }

  /// Go back
  void back<T>([T? result]) {
    if (_goRouter != null && _goRouter!.canPop()) {
      _goRouter!.pop(result);
    } else {
      Get.back<T>(result: result);
    }
  }

  /// Navigate with go_router
  Future<T?>? _navigateWithGoRouter<T>(
    String routeName, {
    dynamic arguments,
    Map<String, String>? parameters,
  }) {
    if (_goRouter == null) return null;

    // Convert arguments to extra data for go_router
    final extra = arguments != null ? {'arguments': arguments} : null;

    return _goRouter!.push<T>(
      routeName,
      extra: extra,
    );
  }

  /// Go with go_router (replaces current route)
  Future<T?>? _goWithGoRouter<T>(
    String routeName, {
    dynamic arguments,
    Map<String, String>? parameters,
  }) {
    if (_goRouter == null) return null;

    // Convert arguments to extra data for go_router
    final extra = arguments != null ? {'arguments': arguments} : null;

    _goRouter!.go(routeName, extra: extra);
    return null;
  }

  /// Get current route name
  String get currentRoute {
    if (_goRouter != null) {
      final location = _goRouter!.routerDelegate.currentConfiguration.uri.path;
      if (location.isNotEmpty) return location;
    }
    return Get.currentRoute;
  }

  /// Check if we can go back
  bool get canPop {
    if (_goRouter != null && _goRouter!.canPop()) {
      return true;
    }
    return Get.isDialogOpen == false && Get.isBottomSheetOpen == false;
  }
}
