import 'dart:convert';
import 'dart:io';

import 'package:app_tracking_transparency/app_tracking_transparency.dart';
import 'package:automoment/app/services/api_client.dart';
import 'package:automoment/app/services/deep_link_service.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import 'app/constants/app_config.dart';
import 'app/controllers/user_controller.dart';
import 'app/globals.dart';
import 'app/helpers/notification_util.dart';
import 'app/routes/app_pages.dart';
import 'app/services/analytics_service.dart';
import 'app/widgets/hybrid_app.dart';
import 'firebase_options.dart';

Future<void> main() async {
  if (!AppConfig.isProduction) {
    HttpOverrides.global = MyHttpOverrides();
  }

  await GetStorage.init();

  // FIREBASE push notification setup

  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase first
  FirebaseApp firebaseApp = await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  firebaseDatabase = FirebaseDatabase.instanceFor(
      app: firebaseApp, databaseURL: AppConfig.firebaseDatabaseUrl);
  firebaseDatabase.setPersistenceEnabled(true);
  firebaseDatabase.setPersistenceCacheSizeBytes(10000000); // 10MB
  firebaseDatabase.setLoggingEnabled(
      !AppConfig.isProduction); // Enable logging only in debug/dev

  firebaseStorage = FirebaseStorage.instance;

  // Initialize Firebase Analytics and register the service
  FirebaseAnalytics analytics = FirebaseAnalytics.instance;
  Get.put(AnalyticsService(), permanent: true);

  FirebaseMessaging messaging = FirebaseMessaging.instance;

  NotificationSettings settings = await messaging.requestPermission(
    alert: true,
    announcement: false,
    badge: true,
    carPlay: false,
    criticalAlert: false,
    provisional: false,
    sound: true,
  );

  if (settings.authorizationStatus == AuthorizationStatus.authorized) {
    debugPrint('User granted permission');
  } else if (settings.authorizationStatus == AuthorizationStatus.provisional) {
    debugPrint('User granted provisional permission');
  } else {
    debugPrint('User declined or has not accepted permission');
  }

  // ANDROID Channel

  const AndroidNotificationChannel channel = AndroidNotificationChannel(
    'high_importance_channel', // id
    'High Importance Notifications', // title
    //'This channel is used for important notifications.', // description
    importance: Importance.max,
  );

  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  // Initialize flutterLocalNotificationsPlugin once here
  const AndroidInitializationSettings initializationSettingsAndroid =
      AndroidInitializationSettings(
          'ic_launcher'); // Ensure 'ic_launcher' is in android/app/src/main/res/mipmap
  const DarwinInitializationSettings initializationSettingsDarwin =
      DarwinInitializationSettings(
          onDidReceiveLocalNotification: onDidReceiveLocalNotification);
  const InitializationSettings initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsDarwin);
  await flutterLocalNotificationsPlugin.initialize(initializationSettings,
      onDidReceiveNotificationResponse: onDidReceiveNotificationResponse);

  await flutterLocalNotificationsPlugin
      .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin>()
      ?.createNotificationChannel(channel);

  // IOS

  await FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
    alert: false, //true, // Required to display a heads up notification
    badge: true,
    sound: true,
  );

  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  // ANDROID foreground

  FirebaseMessaging.onMessage.listen((RemoteMessage message) async {
    var type = message.data['type'];
    var id = message.data['data'];

    debugPrint("onMessage: $type : $id");
    RemoteNotification? notification = message.notification;
    AndroidNotification? android = message.notification?.android;

    // If `onMessage` is triggered with a notification, construct our own
    // local notification to show to users using the created channel.
    if (notification != null && android != null) {
      flutterLocalNotificationsPlugin.show(
          notification.hashCode,
          notification.title,
          notification.body,
          NotificationDetails(
            android: AndroidNotificationDetails(
              channel.id,
              channel.name,
              //channel.description,
              //icon: android?.smallIcon,
              icon: "@mipmap/ic_launcher",
              // other properties...
            ),
          ),
          payload: "${message.data['type']}|${message.data['data']}");
    }
  });

  //
  await AppTrackingTransparency.requestTrackingAuthorization();

  // Determine initial route based on app version, login status, and deep links
  Future<String> determineInitialRoute() async {
    debugPrint('Determining initial route');

    // First check if app version is latest
    if (!await isAppLatestVersion()) {
      debugPrint('App needs upgrade, routing to FORCE_UPGRADE');
      return Routes.FORCE_UPGRADE;
    }

    // Check for user login status
    final userController = Get.put(UserController());
    final isLoggedIn = userController.isUserLoggedIn();

    // Check for deep links
    final deepLinkService = Get.find<DeepLinkService>();
    final pendingEventId = deepLinkService.getPendingEventId();

    if (pendingEventId != null && isLoggedIn) {
      debugPrint(
          'Found pending event ID: $pendingEventId, preparing for navigation');
      // Store the event ID for navigation after app is initialized
      GetStorage().write('initial_event_id', pendingEventId);
      return Routes.BOTTOMBAR;
    }

    // Default routes based on login status
    return isLoggedIn ? Routes.BOTTOMBAR : Routes.GUEST;
  }

  //Uri.base.path;

  // final PendingDynamicLinkData? initialLink = await FirebaseDynamicLinks.instance.getInitialLink();
  // debugPrint("initialLink: ${initialLink?.android.toString()}");

  // Initialize DeepLinkService first
  await Get.putAsync(() => DeepLinkService().init(), permanent: true);
  debugPrint('Main: DeepLinkService initialized');

  // Set up a callback to process any pending deep links after the app is initialized
  Get.put(AppInitializationController());

  await SentryFlutter.init(
    (options) {
      options.debug = false;
      options.dsn =
          'https://<EMAIL>/4507121265868800';
      options.tracesSampleRate = 1.0;
      options.profilesSampleRate = 1.0;
      options.experimental.replay.sessionSampleRate = 1.0;
      options.experimental.replay.onErrorSampleRate = 1.0;
      options.experimental.privacy.maskAllText = false;
      options.experimental.privacy.maskAllImages = false;
      options.experimental.privacy.mask<IconButton>();
      options.experimental.privacy.unmask<Image>();
      options.experimental.privacy.maskCallback<Text>(
          (Element element, Text widget) =>
              (widget.data?.contains('secret') ?? false)
                  ? SentryMaskingDecision.mask
                  : SentryMaskingDecision.continueProcessing);
    },
    appRunner: () async => runApp(
      HybridApp(
        initialRoute: await determineInitialRoute(),
        analytics: analytics,
      ),
    ),
  );
}

Future<bool> isAppLatestVersion() async {
  PackageInfo packageInfo = await PackageInfo.fromPlatform();
  int currentBuildVersion = int.tryParse(packageInfo.buildNumber) ?? 0;

  try {
    String responseString = await ApiClient.checkAppVersions();
    Map<String, dynamic> versions = jsonDecode(responseString);

    int latestAndroidVersion =
        int.tryParse(versions['android']?.toString() ?? '0') ?? 0;
    int latestIOSVersion =
        int.tryParse(versions['ios']?.toString() ?? '0') ?? 0;

    debugPrint('Current build version: $currentBuildVersion');
    debugPrint(
        'Latest Android version (latest | current): $latestAndroidVersion | $currentBuildVersion');
    debugPrint(
        'Latest iOS version (latest | current): $latestIOSVersion | $currentBuildVersion');

    if (Platform.isAndroid) {
      return currentBuildVersion >= latestAndroidVersion;
    } else if (Platform.isIOS) {
      return currentBuildVersion >= latestIOSVersion;
    }
    // Default for other platforms or if platform detection fails
    return true;
  } catch (e, stackTrace) {
    // Log the error and decide on fallback behavior.
    // Returning true allows the app to proceed if the version check API fails.
    // This might be acceptable to avoid blocking users due to a transient API issue.
    debugPrint(
        'Error checking app version: $e. Assuming current version is acceptable.');
    Sentry.captureException(e, stackTrace: stackTrace);
    return true;
  }
}

// void listenToNotification(FirebaseMessaging messaging) {
//   messaging.configure(
//     onMessage: (Map<String, dynamic> message) async {
//       debugPrint("onMessage: $message");
//     },
//     onLaunch: (Map<String, dynamic> message) async {
//       debugPrint("onLaunch: $message");
//     },
//     onResume: (Map<String, dynamic> message) async {
//       debugPrint("onResume: ${message["data"]}");
//       SchedulerBinding.instance.addPostFrameCallback((_) {
//         debugPrint("onResume message: $message");
//       });
//     },
//   );
// }

void onDidReceiveLocalNotification(
    int id, String? title, String? body, String? payload) {
  debugPrint("onDidReceiveLocalNotification");

  // split payload
  var type = payload?.split('|')[0];
  var id = payload?.split('|')[1];

  NotificationUtil().pushNotificationAction(type!, id!);
}

void onDidReceiveNotificationResponse(
    NotificationResponse notificationResponse) {
  debugPrint(
      "onDidReceiveNotificationResponse: ${notificationResponse.payload}");

  // split payload
  var payload = notificationResponse.payload;
  var type = payload?.split('|')[0];
  var id = payload?.split('|')[1];

  NotificationUtil().pushNotificationAction(type!, id!);
}

Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // If you're going to use other Firebase services in the background, such as Firestore,
  // make sure you call `initializeApp` before using other Firebase services.
  await Firebase.initializeApp();

  var type = message.data['type'];
  var id = message.data['data'];

  debugPrint("Handling a background message: ${message.messageId}");
  debugPrint("Handling a background message: ${message.data}");
  debugPrint("Handling a background message: $type : $id");
}

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}

// Controller to handle app initialization and process deep links
class AppInitializationController extends GetxController {
  final DeepLinkService _deepLinkService = Get.find<DeepLinkService>();

  @override
  void onReady() {
    super.onReady();
    // Process any pending deep links after the app is fully initialized
    debugPrint(
        'AppInitializationController: App is ready, processing pending deep links');
    _deepLinkService.processPendingDeepLinks();
  }
}
